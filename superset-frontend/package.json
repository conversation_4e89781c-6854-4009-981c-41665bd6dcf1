{"name": "superset", "version": "4.1.1", "description": "Superset is a data exploration platform designed to be visual, intuitive, and interactive.", "keywords": ["big", "data", "exploratory", "analysis", "react", "d3", "airbnb", "nerds", "database", "flask"], "homepage": "https://superset.apache.org/", "bugs": {"url": "https://github.com/apache/superset/issues"}, "repository": {"type": "git", "url": "https://github.com/apache/superset.git", "directory": "superset-frontend"}, "license": "Apache-2.0", "author": {"name": "Apache"}, "directories": {"doc": "docs", "test": "spec"}, "workspaces": ["packages/*", "plugins/*", "src/setup/*"], "scripts": {"_prettier": "prettier './({src,spec,cypress-base,plugins,packages,.storybook}/**/*{.js,.jsx,.ts,.tsx,.css,.less,.scss,.sass}|package.json)'", "build": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production BABEL_ENV=\"${BABEL_ENV:=production}\" webpack --mode=production --color", "build-dev": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=development webpack --mode=development --color", "build-instrumented": "cross-env NODE_ENV=production BABEL_ENV=instrumented webpack --mode=production --color", "build-storybook": "storybook build", "build-translation": "scripts/po2json.sh", "core:cover": "cross-env NODE_ENV=test jest --coverage --coverageThreshold='{\"global\":{\"statements\":100,\"branches\":100,\"functions\":100,\"lines\":100}}' --collectCoverageFrom='[\"packages/**/src/**/*.{js,ts}\", \"!packages/superset-ui-demo/**/*\"]' packages", "cover": "cross-env NODE_ENV=test jest --coverage", "dev": "webpack --mode=development --color --watch", "dev-server": "cross-env NODE_ENV=development BABEL_ENV=development node --max_old_space_size=4096 ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --mode=development", "eslint": "eslint --ignore-path=.eslintignore --ext .js,.jsx,.ts,tsx", "format": "npm run _prettier -- --write", "lint": "npm run eslint -- . && npm run type", "lint-fix": "npm run eslint -- . --fix --quiet", "lint-stats": "eslint -f ./scripts/eslint-metrics-uploader.js --ignore-path=.eslintignore --ext .js,.jsx,.ts,.tsx . ", "plugins:build": "node ./scripts/build.js", "plugins:build-assets": "node ./scripts/copyAssets.js", "plugins:build-storybook": "cd packages/superset-ui-demo && npm run build-storybook", "plugins:create-conventional-version": "npm run prune && lerna version --conventional-commits --create-release github --no-private --yes", "plugins:create-minor-version": "npm run prune && lerna version minor --no-private --yes", "plugins:create-patch-version": "npm run prune && lerna version patch --no-private --yes", "plugins:release-conventional": "npm run prune && lerna publish --conventional-commits --create-release github --yes", "plugins:release-from-tag": "npm run prune && lerna publish from-package --yes", "plugins:storybook": "cd packages/superset-ui-demo && npm run storybook", "prettier": "npm run _prettier -- --write", "prettier-check": "npm run _prettier -- --check", "prod": "npm run build", "prune": "rm -rf ./{packages,plugins}/*/{lib,esm,tsconfig.tsbuildinfo,package-lock.json}", "storybook": "cross-env NODE_ENV=development BABEL_ENV=development storybook dev -p 6006", "tdd": "cross-env NODE_ENV=test jest --watch", "test": "cross-env NODE_ENV=test jest --max-workers=50%", "type": "tsc --noEmit", "update-maps": "jupyter nbconvert --to notebook --execute --inplace 'plugins/legacy-plugin-chart-country-map/scripts/Country Map GeoJSON Generator.ipynb' -Xfrozen_modules=off", "validate-release": "../RELEASING/validate_this_release.sh", "pl:clean-dev-output": "node --trace-uncaught src/Superstructure/config/cleanDevOutput.js", "pl:dev-server:spr": "npm run pl:clean-dev-output && cross-env NODE_ENV=development DEV_STAND=spr BABEL_ENV=development node --max_old_space_size=4096 ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --mode=development --port 3000 --config webpack.config.plugin.js --env .env", "pl:dev-server:fof": "npm run pl:clean-dev-output && cross-env NODE_ENV=development DEV_STAND=fof BABEL_ENV=development node --max_old_space_size=4096 ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --mode=development --port 3000 --config webpack.config.plugin.js --env .env", "pl:build:dev": "npm run pl:build:app:dev && npm run pl:build:cleanup && npm run pl:build:addCss:dev", "pl:build:local": "npm run pl:build:app:local && npm run pl:build:cleanup && npm run pl:build:addCss:local", "pl:build:prod": "npm run pl:build:app:prod && npm run pl:build:cleanup && npm run pl:build:addCss:prod", "pl:build:addCss:dev": "node --trace-uncaught src/Superstructure/config/addCss/dev.js", "pl:build:addCss:local": "node --trace-uncaught src/Superstructure/config/addCss/local.js", "pl:build:addCss:prod": "node --trace-uncaught src/Superstructure/config/addCss/prod.js", "pl:build:app:dev": "echo 'Started pl:build:app:dev' && cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production BABEL_ENV=production webpack --mode=production --color --progress --config webpack.config.plugin.js --env .env.dev && echo 'Finished pl:build:app:dev'", "pl:build:app:local": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production BABEL_ENV=production webpack --mode=none --color --progress --config webpack.config.plugin.js --env .env.local", "pl:build:app:prod": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production BABEL_ENV=production webpack --mode=production --color --progress --config webpack.config.plugin.js --env .env.prod", "pl:build:cleanup": "node --trace-uncaught src/Superstructure/config/cleanUpFiles.js", "pl:serve:3000": "npx http-server -p 3000 --cors", "pl:serve:6479": "npx http-server -p 6479 --cors"}, "browserslist": ["last 3 chrome versions", "last 3 firefox versions", "last 3 safari versions", "last 3 edge versions"], "dependencies": {"@ant-design/icons": "^5.2.6", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "@fontsource/fira-code": "^5.0.18", "@fontsource/inter": "^5.0.18", "@reduxjs/toolkit": "^1.9.3", "@rollbar/react": "^0.12.1", "@scarf/scarf": "^1.3.0", "@superset-ui/chart-controls": "file:./packages/superset-ui-chart-controls", "@superset-ui/core": "file:./packages/superset-ui-core", "@superset-ui/legacy-plugin-chart-calendar": "file:./plugins/legacy-plugin-chart-calendar", "@superset-ui/legacy-plugin-chart-chord": "file:./plugins/legacy-plugin-chart-chord", "@superset-ui/legacy-plugin-chart-country-map": "file:./plugins/legacy-plugin-chart-country-map", "@superset-ui/legacy-plugin-chart-event-flow": "file:./plugins/legacy-plugin-chart-event-flow", "@superset-ui/legacy-plugin-chart-heatmap": "file:./plugins/legacy-plugin-chart-heatmap", "@superset-ui/legacy-plugin-chart-histogram": "file:./plugins/legacy-plugin-chart-histogram", "@superset-ui/legacy-plugin-chart-horizon": "file:./plugins/legacy-plugin-chart-horizon", "@superset-ui/legacy-plugin-chart-map-box": "file:./plugins/legacy-plugin-chart-map-box", "@superset-ui/legacy-plugin-chart-paired-t-test": "file:./plugins/legacy-plugin-chart-paired-t-test", "@superset-ui/legacy-plugin-chart-parallel-coordinates": "file:./plugins/legacy-plugin-chart-parallel-coordinates", "@superset-ui/legacy-plugin-chart-partition": "file:./plugins/legacy-plugin-chart-partition", "@superset-ui/legacy-plugin-chart-rose": "file:./plugins/legacy-plugin-chart-rose", "@superset-ui/legacy-plugin-chart-sankey": "file:./plugins/legacy-plugin-chart-sankey", "@superset-ui/legacy-plugin-chart-sankey-loop": "file:./plugins/legacy-plugin-chart-sankey-loop", "@superset-ui/legacy-plugin-chart-world-map": "file:./plugins/legacy-plugin-chart-world-map", "@superset-ui/legacy-preset-chart-deckgl": "file:./plugins/legacy-preset-chart-deckgl", "@superset-ui/legacy-preset-chart-nvd3": "file:./plugins/legacy-preset-chart-nvd3", "@superset-ui/plugin-chart-echarts": "file:./plugins/plugin-chart-echarts", "@superset-ui/plugin-chart-handlebars": "file:./plugins/plugin-chart-handlebars", "@superset-ui/plugin-chart-pivot-table": "file:./plugins/plugin-chart-pivot-table", "@superset-ui/plugin-chart-table": "file:./plugins/plugin-chart-table", "@superset-ui/plugin-chart-word-cloud": "file:./plugins/plugin-chart-word-cloud", "@superset-ui/switchboard": "file:./packages/superset-ui-switchboard", "@types/d3-format": "^3.0.1", "@types/d3-time-format": "^3.0.1", "@visx/axis": "^3.8.0", "@visx/grid": "^3.5.0", "@visx/responsive": "^3.0.0", "@visx/scale": "^3.5.0", "@visx/tooltip": "^3.0.0", "@visx/xychart": "^3.5.1", "abortcontroller-polyfill": "^1.1.9", "ace-builds": "^1.4.14", "antd": "4.10.3", "antd-v5": "npm:antd@^5.18.0", "axios": "^1.7.9", "babel-plugin-typescript-to-proptypes": "^2.0.0", "bootstrap": "^3.4.1", "brace": "^0.11.1", "chrono-node": "^2.7.5", "classnames": "^2.2.5", "core-js": "^3.37.1", "currencyformatter.js": "^1.0.5", "d3-scale": "^2.1.2", "dom-to-image-more": "^3.2.0", "dom-to-pdf": "^0.3.2", "dotenv": "^16.4.7", "emotion-rgba": "0.0.12", "fast-glob": "^3.2.7", "file-saver": "^2.0.5", "find-remove": "^4.1.0", "firebase": "^9.23.0", "fs-extra": "^10.0.0", "fuse.js": "^7.0.0", "geolib": "^2.0.24", "googleapis": "^130.0.0", "html-webpack-plugin": "^5.3.2", "immer": "^9.0.6", "interweave": "^13.1.0", "jquery": "^3.5.1", "js-levenshtein": "^1.1.6", "js-yaml-loader": "^1.2.2", "json-bigint": "^1.0.0", "json-stringify-pretty-compact": "^2.0.0", "lodash": "^4.17.21", "luxon": "^3.5.0", "mapbox-gl": "^2.10.0", "markdown-to-jsx": "^7.4.7", "match-sorter": "^6.3.4", "memoize-one": "^5.1.1", "moment": "^2.30.1", "moment-timezone": "^0.5.44", "mousetrap": "^1.6.5", "mustache": "^2.2.1", "nanoid": "^5.0.7", "polished": "^4.3.1", "prepend-file": "^2.0.1", "prop-types": "^15.8.1", "query-string": "^6.13.7", "rc-trigger": "^5.3.4", "re-resizable": "^6.9.11", "react": "^16.13.1", "react-ace": "^10.1.0", "react-checkbox-tree": "^1.8.0", "react-color": "^2.13.8", "react-diff-viewer-continued": "^3.2.5", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^16.13.1", "react-draggable": "^4.4.6", "react-hot-loader": "^4.13.1", "react-intersection-observer": "^9.10.2", "react-js-cron": "^2.1.2", "react-json-tree": "^0.17.0", "react-jsonschema-form": "^1.8.1", "react-lines-ellipsis": "^0.15.4", "react-loadable": "^5.5.0", "react-redux": "^7.2.9", "react-resize-detector": "^7.1.2", "react-reverse-portal": "^2.1.1", "react-router-dom": "^5.3.4", "react-search-input": "^0.11.3", "react-sortable-hoc": "^2.0.0", "react-split": "^2.0.9", "react-syntax-highlighter": "^15.4.5", "react-table": "^7.8.0", "react-transition-group": "^2.5.3", "react-ultimate-pagination": "^1.3.0", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "redux": "^4.2.1", "redux-localstorage": "^0.4.1", "redux-thunk": "^2.1.0", "redux-undo": "^1.0.0-beta9-9-7", "regenerator-runtime": "^0.14.1", "rimraf": "^3.0.2", "rison": "^0.1.1", "rollbar": "^2.26.4", "scroll-into-view-if-needed": "^3.1.0", "shortid": "^2.2.6", "single-spa-react": "^4.6.1", "sprintf-js": "^1.1.1", "tinycolor2": "^1.4.2", "ua-parser-js": "^2.0.0", "urijs": "^1.19.8", "use-event-callback": "^0.1.0", "use-immer": "^0.9.0", "use-query-params": "^1.1.9", "yargs": "^17.7.2"}, "devDependencies": {"@applitools/eyes-storybook": "^3.50.7", "@babel/cli": "^7.22.6", "@babel/compat-data": "^7.22.6", "@babel/core": "^7.23.9", "@babel/eslint-parser": "^7.23.10", "@babel/node": "^7.22.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.22.7", "@babel/preset-env": "^7.22.7", "@babel/preset-react": "^7.22.5", "@babel/register": "^7.23.7", "@cypress/react": "^5.10.0", "@emotion/babel-plugin": "^11.11.0", "@emotion/jest": "^11.11.0", "@hot-loader/react-dom": "^16.14.0", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@mihkeleidast/storybook-addon-source": "^1.0.1", "@storybook/addon-actions": "^8.1.11", "@storybook/addon-controls": "^8.1.11", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-links": "^8.1.11", "@storybook/addon-mdx-gfm": "^8.1.11", "@storybook/components": "^8.1.11", "@storybook/preview-api": "^8.1.11", "@storybook/react": "^8.1.11", "@storybook/react-webpack5": "^8.1.11", "@svgr/webpack": "^8.0.1", "@testing-library/dom": "^7.29.4", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.0", "@testing-library/react-hooks": "^5.1.3", "@testing-library/user-event": "^12.7.0", "@types/classnames": "^2.2.10", "@types/dom-to-image": "^2.6.7", "@types/enzyme": "^3.10.18", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/fetch-mock": "^7.3.2", "@types/jest": "^29.5.12", "@types/jquery": "^3.5.8", "@types/js-levenshtein": "^1.1.3", "@types/json-bigint": "^1.0.4", "@types/mousetrap": "^1.6.15", "@types/react": "^16.9.53", "@types/react-dom": "^16.9.8", "@types/react-gravatar": "^2.6.14", "@types/react-json-tree": "^0.6.11", "@types/react-jsonschema-form": "^1.7.4", "@types/react-loadable": "^5.5.6", "@types/react-redux": "^7.1.10", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-table": "^7.7.19", "@types/react-transition-group": "^4.4.10", "@types/react-ultimate-pagination": "^1.2.0", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/redux-localstorage": "^1.0.8", "@types/redux-mock-store": "^1.0.6", "@types/rison": "0.0.9", "@types/shortid": "^0.0.29", "@types/sinon": "^9.0.5", "@types/tinycolor2": "^1.4.3", "@types/yargs": "12 - 18", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-jest": "^26.6.3", "babel-loader": "^9.1.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-jsx-remove-data-test-id": "^3.0.0", "babel-plugin-lodash": "^3.3.4", "copy-webpack-plugin": "^12.0.2", "cross-env": "^5.2.1", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.7", "esbuild": "^0.20.0", "esbuild-loader": "^4.1.0", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^7.2.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-cypress": "^2.11.2", "eslint-plugin-file-progress": "^1.2.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jest": "^27.8.0", "eslint-plugin-jest-dom": "^3.6.5", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-no-only-tests": "^2.4.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-prefer-function-component": "^0.0.7", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-theme-colors": "file:tools/eslint-plugin-theme-colors", "eslint-plugin-translation-vars": "file:tools/eslint-plugin-translation-vars", "exports-loader": "^5.0.0", "fetch-mock": "^7.7.3", "fork-ts-checker-webpack-plugin": "^6.5.3", "history": "^4.10.1", "ignore-styles": "^5.0.1", "imports-loader": "^5.0.0", "jest": "^29.7.0", "jest-environment-enzyme": "^7.1.2", "jest-environment-jsdom": "^29.7.0", "jest-enzyme": "^7.1.2", "jest-html-reporter": "^3.10.2", "jest-websocket-mock": "^2.5.0", "jsdom": "^24.0.0", "lerna": "^8.1.5", "less": "^4.2.0", "less-loader": "^10.2.0", "mini-css-extract-plugin": "^2.7.6", "mock-socket": "^9.3.1", "node-fetch": "^2.6.7", "po2json": "^0.4.5", "prettier": "3.1.0", "prettier-plugin-packagejson": "^2.4.10", "process": "^0.11.10", "react-resizable": "^3.0.5", "react-test-renderer": "^16.14.0", "redux-mock-store": "^1.5.4", "sinon": "^9.0.2", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "speed-measure-webpack-plugin": "^1.5.0", "storybook": "^8.1.11", "style-loader": "^3.3.4", "thread-loader": "^3.0.4", "transform-loader": "^0.2.4", "ts-loader": "^9.4.4", "typescript": "^4.8.4", "vm-browserify": "^1.1.2", "webpack": "^5.88.1", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.1", "webpack-manifest-plugin": "^4.1.1", "webpack-sources": "^3.2.3", "xdm": "^3.4.0"}, "engines": {"node": "^18.19.1", "npm": "^10.2.4"}, "overrides": {"d3-color": "^3.1.0", "yosay": {"ansi-regex": "^4.1.1"}, "puppeteer": "^22.4.1", "@types/react": "^16.9.53"}, "readme": "ERROR: No README data found!", "scarfSettings": {"allowTopLevel": true}, "_id": "superset@0.0.0-dev"}