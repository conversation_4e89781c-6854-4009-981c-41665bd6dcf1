# DODO was here
# Team Carousel UI On Home page #52010498
"""add tag_id to teams

Revision ID: a1b2c3d4e5f6
Revises: 48cbb571fa3a
Create Date: 2025-01-07 19:30:00.000000

"""

import sqlalchemy as sa
from alembic import op

from superset.migrations.shared.utils import add_column_if_not_exists

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "48cbb571fa3a"


def upgrade():
    """Add tag_id column to teams table."""
    add_column_if_not_exists(
        "teams",
        sa.Column("tag_id", sa.In<PERSON>ger(), sa.<PERSON>("tag.id"), nullable=True),
    )


def downgrade():
    """Remove tag_id column from teams table."""
    try:
        op.drop_column("teams", "tag_id")
    except Exception:
        # Column might not exist, ignore the error
        pass
